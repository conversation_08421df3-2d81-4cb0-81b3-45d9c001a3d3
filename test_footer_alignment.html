<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Alignment Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 测试样式 - 添加边框来可视化对齐 */
        .test-container {
            min-height: 80vh;
            padding: 20px;
            border: 2px solid #ddd;
            margin: 20px;
        }
        
        .main-footer {
            border: 3px solid red;
            background-color: #f9f9f9;
        }
        
        .visitor-counter {
            border: 2px solid blue;
            background-color: #fff;
        }
        
        .footer-info {
            border: 2px solid green;
            background-color: #f0f0f0;
        }
        
        .content-spacer {
            height: 300px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; margin-bottom: 20px;">页脚居中对齐测试</h1>
        
        <div class="content-spacer">
            <p>这里是页面内容区域</p>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <div class="visitor-counter">
                <div style="width: 300px; height: 150px; background-color: #ddd; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                    <p>访问者计数器模拟</p>
                </div>
            </div>

            <div class="footer-info">
                <p>&copy; 2025 Yang Yang | Site last updated: 2025-01-18 | Total Visits 1234</p>
            </div>
        </footer>
    </div>

    <div style="text-align: center; margin: 20px; color: #666;">
        <p>测试说明：</p>
        <p>红色边框：页脚容器 (.main-footer)</p>
        <p>蓝色边框：访问者计数器 (.visitor-counter)</p>
        <p>绿色边框：页脚信息 (.footer-info)</p>
        <p>在移动端，所有元素应该居中对齐</p>
        <p>请调整浏览器窗口大小测试不同屏幕尺寸的效果</p>
    </div>
</body>
</html>
