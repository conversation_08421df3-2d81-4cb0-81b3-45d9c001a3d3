<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端文字对齐测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        .width-indicator {
            background: #f0f0f0;
            padding: 10px;
            margin-bottom: 20px;
            text-align: center;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="width-indicator">
            当前容器宽度: <span id="width-display"></span>px
        </div>
        
        <div class="test-section">
            <div class="test-title">修复前的效果 (两端对齐):</div>
            <p style="text-align: justify;">
                I am a Computer Technology MS student at School of Computer Science (School of Artificial Intelligence), South-Central Minzu University, advised by Dr. Jianlin Zhu (朱剑林). My research lies in the interdisciplinary areas of artificial intelligence and medical image analysis, aiming at advancing healthcare with machine intelligence.
            </p>
        </div>
        
        <div class="test-section">
            <div class="test-title">修复后的效果 (左对齐):</div>
            <div class="about-section">
                <p>
                    I am a Computer Technology MS student at School of Computer Science (School of Artificial Intelligence), South-Central Minzu University, advised by Dr. Jianlin Zhu (朱剑林). My research lies in the interdisciplinary areas of artificial intelligence and medical image analysis, aiming at advancing healthcare with machine intelligence.
                </p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">说明:</div>
            <p style="text-align: left; font-size: 14px; color: #666;">
                在小屏幕设备上（宽度 ≤ 768px），文字会自动从两端对齐切换为左对齐，避免文字间距过大的问题。
                请调整浏览器窗口大小或使用开发者工具的设备模拟器来测试效果。
            </p>
        </div>
    </div>

    <script>
        function updateWidth() {
            const container = document.querySelector('.test-container');
            const width = container.offsetWidth;
            document.getElementById('width-display').textContent = width;
        }
        
        window.addEventListener('resize', updateWidth);
        updateWidth();
    </script>
</body>
</html>
