<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Footer Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-content {
            min-height: 60vh;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            margin-bottom: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .screen-size-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-size-indicator" id="screenSize">
        屏幕宽度: <span id="width"></span>px
    </div>

    <div class="container">
        <div class="test-content">
            <h1>页脚居中对齐测试</h1>
            <p>这是一个测试页面，用于验证页脚在不同屏幕尺寸下的居中对齐效果。</p>
            <p>请调整浏览器窗口大小来测试响应式效果。</p>
            <ul style="text-align: left; margin-top: 20px;">
                <li>桌面端 (> 900px): 正常显示</li>
                <li>平板端 (480px - 900px): 中等缩放</li>
                <li>手机端 (< 480px): 小尺寸优化</li>
            </ul>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <div class="visitor-counter">
                <script type="text/javascript" id="clustrmaps"
                    src="//clustrmaps.com/map_v2.js?d=_AhZGM9DqgNcORvW5D0isv0WXJPU1XWopy7ZarZtYuA&cl=ffffff&w=300">
                </script>
            </div>

            <div class="footer-info">
                <p>&copy; 2025 Yang Yang | Site last updated: 2025-01-18 | Total Visits 1234</p>
            </div>
        </footer>
    </div>

    <script>
        function updateScreenSize() {
            const width = window.innerWidth;
            document.getElementById('width').textContent = width;
            
            let sizeCategory = '';
            if (width > 900) {
                sizeCategory = ' (桌面端)';
            } else if (width > 480) {
                sizeCategory = ' (平板端)';
            } else {
                sizeCategory = ' (手机端)';
            }
            
            document.getElementById('screenSize').innerHTML = 
                `屏幕宽度: <span id="width">${width}</span>px${sizeCategory}`;
        }

        window.addEventListener('resize', updateScreenSize);
        updateScreenSize();
    </script>
</body>
</html>
