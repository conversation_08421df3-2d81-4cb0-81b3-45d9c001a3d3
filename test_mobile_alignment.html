<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Education Alignment Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 测试样式 - 添加边框来可视化对齐 */
        .test-container {
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            border: 2px solid #ddd;
        }
        
        .test-container h2 {
            border-left: 3px solid red;
            padding-left: 5px;
            background-color: #f0f0f0;
        }
        
        .education-list {
            border-left: 3px solid blue;
            background-color: #f9f9f9;
        }
        
        .edu-period {
            border-left: 3px solid green;
            background-color: #fff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Education</h2>
        <div class="education-list">
            <div class="education-item">
                <div class="edu-period">2024 - 2027 (Expected)</div>
                <div class="edu-content">
                    <div class="edu-info-container">
                        <div class="edu-logo">
                            <img src="https://yangyang666.oss-cn-chengdu.aliyuncs.com/academic/scuec.ico" alt="University" class="university-logo">
                        </div>
                        <div class="edu-details">
                            <p>Master of Computer Technology</p>
                            <p class="edu-focus">Focus: Multimodal Medical Image Analysis and Computer Vision</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="education-item">
                <div class="edu-period">2020 - 2024</div>
                <div class="edu-content">
                    <div class="edu-info-container">
                        <div class="edu-logo">
                            <img src="https://yangyang666.oss-cn-chengdu.aliyuncs.com/academic/scuec.ico" alt="University" class="university-logo">
                        </div>
                        <div class="edu-details">
                            <p>Bachelor of Network Engineering</p>
                            <p class="edu-achievement">Graduated with honors</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin: 20px; color: #666;">
        <p>测试说明：</p>
        <p>红色边框：Education标题</p>
        <p>蓝色边框：教育列表容器</p>
        <p>绿色边框：日期元素</p>
        <p>在移动端（宽度 < 900px），所有元素应该左对齐</p>
    </div>
</body>
</html>
